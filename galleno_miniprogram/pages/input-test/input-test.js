// input-test.js
Page({
  data: {
    testData: {
      name: '',
      phone: '',
      idCard: ''
    }
  },

  onLoad: function (options) {
    console.log('测试页面加载');
  },

  onInputChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    console.log(`输入变化 - 字段: ${field}, 值: ${value}`);
    
    this.setData({
      [`testData.${field}`]: value
    }, () => {
      console.log(`数据更新完成 - ${field}: ${this.data.testData[field]}`);
      console.log('当前所有数据:', this.data.testData);
    });
  },

  clearAll: function () {
    this.setData({
      testData: {
        name: '',
        phone: '',
        idCard: ''
      }
    });
    console.log('已清空所有数据');
  },

  fillTest: function () {
    this.setData({
      testData: {
        name: '张三',
        phone: '13800138000',
        idCard: '110101199001011234'
      }
    });
    console.log('已填入测试数据');
  }
});
