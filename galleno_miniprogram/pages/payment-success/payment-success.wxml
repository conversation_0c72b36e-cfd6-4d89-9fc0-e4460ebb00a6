<!--payment-success.wxml-->
<view class="container">
  <!-- 成功状态 -->
  <view class="success-section">
    <view class="success-icon">
      <text class="icon">✓</text>
    </view>
    <text class="success-title">支付成功</text>
    <text class="success-subtitle">您的预订已确认，定金支付成功</text>
  </view>

  <!-- 订单信息 -->
  <view class="order-section">
    <view class="section-title">订单信息</view>
    
    <view class="order-card">
      <view class="order-header">
        <text class="order-number">订单号：{{orderData.orderNo}}</text>
        <view class="order-status paid">
          <text class="status-text">已支付</text>
        </view>
      </view>
      
      <view class="order-content">
        <view class="order-row">
          <text class="order-label">会议名称</text>
          <text class="order-value">{{orderData.conferenceTitle}}</text>
        </view>
        
        <view class="order-row">
          <text class="order-label">房型</text>
          <text class="order-value">{{orderData.roomName}}</text>
        </view>
        
        <view class="order-row">
          <text class="order-label">入住时间</text>
          <text class="order-value">{{orderData.checkinDate}} 至 {{orderData.checkoutDate}}</text>
        </view>
        
        <view class="order-row">
          <text class="order-label">住宿天数</text>
          <text class="order-value">{{orderData.nights}}晚</text>
        </view>
        
        <view class="order-row">
          <text class="order-label">入住人</text>
          <text class="order-value">{{orderData.guestInfo.name}}</text>
        </view>
        
        <view class="order-row">
          <text class="order-label">联系电话</text>
          <text class="order-value">{{orderData.guestInfo.phone}}</text>
        </view>
      </view>
      
      <view class="order-footer">
        <view class="price-info">
          <view class="price-row">
            <text class="price-label">房费总计</text>
            <text class="price-value">¥{{orderData.totalPrice}}</text>
          </view>
          <view class="price-row deposit">
            <text class="price-label">已付定金</text>
            <text class="price-value">¥{{orderData.depositAmount}}</text>
          </view>
          <view class="price-row remaining">
            <text class="price-label">到店支付</text>
            <text class="price-value">¥{{orderData.totalPrice - orderData.depositAmount}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 重要提醒 -->
  <view class="reminder-section">
    <view class="section-title">重要提醒</view>
    
    <view class="reminder-card">
      <view class="reminder-item">
        <text class="reminder-icon">📋</text>
        <view class="reminder-content">
          <text class="reminder-title">入住凭证</text>
          <text class="reminder-text">请凭此订单和有效身份证件办理入住手续</text>
        </view>
      </view>
      
      <view class="reminder-item">
        <text class="reminder-icon">💰</text>
        <view class="reminder-content">
          <text class="reminder-title">余款支付</text>
          <text class="reminder-text">入住时需支付剩余房费¥{{orderData.totalPrice - orderData.depositAmount}}</text>
        </view>
      </view>
      
      <view class="reminder-item">
        <text class="reminder-icon">⏰</text>
        <view class="reminder-content">
          <text class="reminder-title">入住时间</text>
          <text class="reminder-text">酒店入住时间为14:00，退房时间为12:00</text>
        </view>
      </view>
      
      <view class="reminder-item">
        <text class="reminder-icon">📞</text>
        <view class="reminder-content">
          <text class="reminder-title">联系方式</text>
          <text class="reminder-text">如有疑问请联系客服：400-888-9999</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-buttons">
      <button class="action-btn secondary" bindtap="viewOrderDetail">
        查看订单详情
      </button>
      <button class="action-btn primary" bindtap="backToHome">
        返回首页
      </button>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>
