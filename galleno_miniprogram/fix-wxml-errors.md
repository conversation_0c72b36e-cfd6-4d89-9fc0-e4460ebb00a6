# WXML 编译错误修复说明

## 问题描述
小程序编译时出现以下错误：
```
[ WXML 文件编译错误] 
Error: Failed to compile WXML files:
- /pages/order-list/order-list.wxml:91:61-91:61: Fatal: unmatched parenthesis
- WXS compile error at /pages/order-list/order-list.wxml: Template error: no template "pages/order-list/order-list" found
```

## 问题原因
在 WXML 文件中使用了 JavaScript 的 `find` 方法和复杂的表达式，这在小程序的 WXML 模板中是不支持的：

```xml
<!-- 错误的写法 -->
<text class="empty-title">
  {{activeTab === 'all' ? '暂无订单' : '暂无' + (tabs.find(tab => tab.key === activeTab) || {}).name + '订单'}}
</text>
```

## 修复方案

### 1. 简化 WXML 模板
将复杂的逻辑从 WXML 中移除，使用简单的数据绑定：

```xml
<!-- 修复后的写法 -->
<text class="empty-title">{{emptyTitle}}</text>
<text class="empty-subtitle">{{emptySubtitle}}</text>
```

### 2. 在 JavaScript 中处理逻辑
在 `order-list.js` 中添加数据字段和处理方法：

```javascript
// 添加数据字段
data: {
  emptyTitle: '暂无订单',
  emptySubtitle: '预订酒店后，订单信息将在这里显示',
  // ... 其他字段
},

// 添加更新空状态文本的方法
updateEmptyText: function (tabKey) {
  let emptyTitle = '暂无订单';
  let emptySubtitle = '预订酒店后，订单信息将在这里显示';

  if (tabKey !== 'all') {
    const currentTab = this.data.tabs.find(tab => tab.key === tabKey);
    if (currentTab) {
      emptyTitle = `暂无${currentTab.name}订单`;
      emptySubtitle = '切换到其他标签查看更多订单';
    }
  }

  this.setData({
    emptyTitle: emptyTitle,
    emptySubtitle: emptySubtitle
  });
}
```

### 3. 在适当的时机调用更新方法
- 标签切换时：`onTabTap` 方法中调用 `updateEmptyText`
- 数据加载完成时：`processOrderData` 和 `loadLocalOrderData` 方法中调用

## 修复的文件

### 修改的文件列表
1. `pages/order-list/order-list.wxml` - 简化模板表达式
2. `pages/order-list/order-list.js` - 添加数据处理逻辑

### 关键修改点

#### WXML 修改
```xml
<!-- 修改前 -->
<text class="empty-title">
  {{activeTab === 'all' ? '暂无订单' : '暂无' + (tabs.find(tab => tab.key === activeTab) || {}).name + '订单'}}
</text>

<!-- 修改后 -->
<text class="empty-title">{{emptyTitle}}</text>
```

#### JavaScript 修改
```javascript
// 添加数据字段
data: {
  emptyTitle: '暂无订单',
  emptySubtitle: '预订酒店后，订单信息将在这里显示',
}

// 添加更新方法
updateEmptyText: function (tabKey) {
  // 根据当前标签动态设置空状态文本
}

// 在标签切换时调用
onTabTap: function (e) {
  // ...
  this.updateEmptyText(tabKey);
}
```

## 小程序 WXML 语法限制

### 不支持的语法
1. **复杂的 JavaScript 表达式**：如 `array.find()`, `array.map()` 等
2. **函数调用**：除了内置的过滤器外，不能调用自定义函数
3. **复杂的对象操作**：如 `obj.prop || {}`

### 推荐的做法
1. **保持模板简单**：只使用简单的数据绑定和条件渲染
2. **逻辑放在 JS 中**：复杂的数据处理在 JavaScript 中完成
3. **使用计算属性**：通过 `setData` 更新计算后的结果

## 验证修复
修复后，小程序应该能够正常编译和运行，不再出现 WXML 编译错误。

### 测试步骤
1. 重新编译小程序项目
2. 进入订单列表页面
3. 切换不同的标签页
4. 验证空状态文本是否正确显示

## 总结
这次修复主要是将 WXML 中不支持的复杂 JavaScript 表达式移到了 JavaScript 代码中处理，遵循了小程序的最佳实践：
- WXML 负责展示
- JavaScript 负责逻辑处理
- 通过 `setData` 进行数据绑定
