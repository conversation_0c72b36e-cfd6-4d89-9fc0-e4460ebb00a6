# 按钮文字居中和大小统一修复说明

## 问题描述
订单待支付页面中的"取消订单"和"立即支付"按钮存在以下问题：
1. 按钮文字在按钮中不居中
2. 两个按钮的文字大小不一致
3. 按钮样式不统一

## 问题原因分析

### 1. 文字不居中的原因
- 使用了 `line-height` 来实现垂直居中，这种方法在小程序中不够稳定
- 没有使用 Flexbox 布局来确保文字完全居中
- 按钮可能存在默认的 padding 和 margin 影响布局

### 2. 文字大小不一致的原因
- 两个按钮的 `font-weight` 设置不同（一个是 `500`，一个是 `bold`）
- 可能存在继承的样式影响

## 修复方案

### 1. 统一使用 Flexbox 布局
将按钮的布局方式从 `line-height` 改为 Flexbox，确保文字完全居中：

```css
.cancel-btn, .pay-btn {
  display: flex;
  align-items: center;      /* 垂直居中 */
  justify-content: center;  /* 水平居中 */
  padding: 0;
  margin: 0;
}
```

### 2. 统一字体样式
确保两个按钮使用相同的字体大小和粗细：

```css
.cancel-btn, .pay-btn {
  font-size: 28rpx;
  font-weight: 500;  /* 统一使用 500 */
}
```

### 3. 添加盒模型控制
确保按钮的尺寸计算准确：

```css
.cancel-btn, .pay-btn {
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
}
```

## 具体修复内容

### 修复前的问题代码
```css
.cancel-btn {
  line-height: 80rpx;  /* 不稳定的居中方式 */
  text-align: center;
  font-weight: 500;
}

.pay-btn {
  line-height: 80rpx;  /* 不稳定的居中方式 */
  text-align: center;
  font-weight: bold;   /* 与取消按钮不一致 */
}
```

### 修复后的代码
```css
.cancel-btn {
  width: 180rpx;
  height: 80rpx;
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
}

.pay-btn {
  flex: 1;
  max-width: 240rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;  /* 与取消按钮保持一致 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
}
```

## 修复要点说明

### 1. Flexbox 居中
- `display: flex` - 启用 Flexbox 布局
- `align-items: center` - 垂直居中
- `justify-content: center` - 水平居中

### 2. 重置默认样式
- `padding: 0` - 移除默认内边距
- `margin: 0` - 移除默认外边距
- `box-sizing: border-box` - 确保尺寸计算包含边框

### 3. 文字处理
- `white-space: nowrap` - 防止文字换行
- `overflow: hidden` - 隐藏溢出内容
- `font-weight: 500` - 统一字体粗细

### 4. 移除小程序默认样式
- `::after { border: none; }` - 移除小程序按钮的默认边框

## 修复效果

### 视觉效果改进
1. **文字完全居中**: 使用 Flexbox 确保文字在按钮中完全居中
2. **样式统一**: 两个按钮的字体大小和粗细完全一致
3. **布局稳定**: 在不同设备和屏幕尺寸下都能保持一致的显示效果

### 技术改进
1. **更可靠的布局**: Flexbox 比 line-height 更稳定
2. **更好的兼容性**: 在各种小程序环境下都能正常显示
3. **更易维护**: 样式更加清晰和统一

## 测试验证

### 测试要点
- [ ] 按钮文字在垂直和水平方向都完全居中
- [ ] 两个按钮的文字大小完全一致
- [ ] 在不同设备上显示效果一致
- [ ] 加载状态下文字仍然居中
- [ ] 按钮点击区域正确

### 测试场景
1. **不同设备**: iPhone、Android 各种屏幕尺寸
2. **不同状态**: 正常状态、加载状态、禁用状态
3. **不同文字长度**: "取消订单"、"立即支付"、"支付中..."

## 总结

通过这次修复，解决了按钮文字居中和大小不一致的问题：

1. **使用 Flexbox 布局** 替代 line-height，确保文字完全居中
2. **统一字体样式** 确保两个按钮的文字大小和粗细一致
3. **重置默认样式** 避免小程序默认样式的干扰
4. **添加盒模型控制** 确保布局的准确性和稳定性

修复后的按钮应该在所有设备和状态下都能正确显示，提供一致的用户体验。
