// payment.js - 微信支付工具类
const api = require('./api.js')

/**
 * 微信支付V3版本工具类
 */
class PaymentUtils {

    /**
     * 创建酒店订单并调起支付
     * @param {Object} orderData 完整的订单数据
     * @param {string} orderData.conferenceId 会议ID
     * @param {string} orderData.roomId 房间ID
     * @param {string} orderData.roomType 房间类型
     * @param {string} orderData.roomName 房间名称
     * @param {string} orderData.checkinDate 入住日期
     * @param {string} orderData.checkoutDate 退房日期
     * @param {number} orderData.nights 住宿天数
     * @param {number} orderData.roomPrice 房间单价
     * @param {number} orderData.totalAmount 订单总金额
     * @param {number} orderData.depositAmount 押金金额
     * @param {string} orderData.guestName 入住人姓名
     * @param {string} orderData.guestPhone 入住人电话
     * @param {string} orderData.guestIdCard 入住人身份证号（可选）
     * @param {string} orderData.specialRequirements 特殊要求（可选）
     * @param {string} orderData.remark 备注（可选）
     * @returns {Promise}
     */
    static createPayment(orderData) {
        return new Promise((resolve, reject) => {
            wx.showLoading({
                title: '正在创建订单...'
            })

            // 调用后端接口创建酒店订单并发起支付
            api.post('/hotel/order/create', orderData)
                .then(res => {
                    wx.hideLoading()
                    const payParams = res
                    console.log('创建酒店订单成功', payParams)

                    // 调起微信支付
                    PaymentUtils.requestPayment(payParams)
                        .then((payResult) => {
                            // 支付成功，返回包含订单信息的结果
                            resolve({
                                ...payResult,
                                orderNo: payParams.orderNo,
                                orderId: payParams.orderId
                            })
                        })
                        .catch(reject)
                })
                .catch(err => {
                    wx.hideLoading()
                    console.error('创建酒店订单异常', err)
                    reject(err)
                })
        })
    }

    /**
     * 调起微信支付
     * @param {Object} payParams 支付参数
     * @returns {Promise}
     */
    static requestPayment(payParams) {
        return new Promise((resolve, reject) => {
            // 调试：打印支付参数
            console.log('准备调起微信支付，参数：', payParams)
            console.log('支付参数详情：', {
                timeStamp: payParams.timeStamp,
                nonceStr: payParams.nonceStr,
                package: payParams.packageValue,
                signType: payParams.signType,
                paySign: payParams.paySign
            })

            // 验证必要参数
            if (!payParams.timeStamp || !payParams.nonceStr || !payParams.packageValue || !payParams.signType || !payParams.paySign) {
                console.error('支付参数不完整：', payParams)
                reject(new Error('支付参数不完整'))
                return
            }

            wx.showLoading({
                title: '正在调起支付...'
            })

            wx.requestPayment({
                timeStamp: payParams.timeStamp,
                nonceStr: payParams.nonceStr,
                package: payParams.packageValue,
                signType: payParams.signType,
                paySign: payParams.paySign,
                success: (res) => {
                    wx.hideLoading()
                    console.log('支付成功', res)
                    console.log('使用微信支付V3版本（RSA签名）')

                    wx.showToast({
                        title: '支付成功',
                        icon: 'success'
                    })

                    resolve(res)
                },
                fail: (err) => {
                    wx.hideLoading()
                    console.error('支付失败详细信息：', err)
                    console.error('错误消息：', err.errMsg)
                    console.error('错误代码：', err.errCode)

                    if (err.errMsg === 'requestPayment:fail cancel') {
                        // 用户取消支付，不显示toast，直接返回取消错误
                        reject(new Error('用户取消支付'))
                    } else {
                        // 分析具体错误原因
                        let errorContent = '支付过程中出现问题，请重试或联系客服'
                        if (err.errMsg) {
                            if (err.errMsg.includes('param')) {
                                errorContent = '支付参数错误，请重新下单'
                            } else if (err.errMsg.includes('network')) {
                                errorContent = '网络连接异常，请检查网络后重试'
                            } else if (err.errMsg.includes('system')) {
                                errorContent = '系统繁忙，请稍后重试'
                            }
                        }

                        wx.showModal({
                            title: '支付失败',
                            content: errorContent,
                            showCancel: true,
                            cancelText: '重试',
                            confirmText: '联系客服',
                            success: (modalRes) => {
                                if (modalRes.confirm) {
                                    // 联系客服
                                    wx.makePhoneCall({
                                        phoneNumber: '4008889999',
                                        fail: () => {
                                            wx.showToast({
                                                title: '拨打失败',
                                                icon: 'none'
                                            })
                                        }
                                    })
                                }
                            }
                        })
                        reject(err)
                    }
                }
            })
        })
    }

    /**
     * 使用已有prepay_id重新调起支付
     * @param {string} orderNo 订单号
     * @returns {Promise}
     */
    static retryPaymentWithPrepayId(orderNo) {
        return new Promise((resolve, reject) => {
            wx.showLoading({
                title: '正在获取支付信息...'
            })

            // 调用后端接口获取已有的支付参数
            api.get('/hotel/order/payment-params', { orderNo })
                .then(res => {
                    wx.hideLoading()
                    console.log('获取支付参数成功', res)

                    if (res && res.timeStamp && res.nonceStr && res.packageValue && res.signType && res.paySign) {
                        // 直接调起微信支付
                        PaymentUtils.requestPayment(res)
                            .then(resolve)
                            .catch(reject)
                    } else {
                        // 支付参数无效，需要重新创建订单
                        reject(new Error('PREPAY_ID_INVALID'))
                    }
                })
                .catch(err => {
                    wx.hideLoading()
                    console.error('获取支付参数异常', err)
                    // 如果是404或者prepay_id过期，返回特殊错误码
                    if (err.message && (err.message.includes('404') || err.message.includes('过期'))) {
                        reject(new Error('PREPAY_ID_INVALID'))
                    } else {
                        reject(err)
                    }
                })
        })
    }

    /**
     * 查询支付订单状态
     * @param {string} outTradeNo 商户订单号
     * @returns {Promise}
     */
    static queryPaymentStatus(outTradeNo) {
        return new Promise((resolve, reject) => {
            api.get('/hotel/payment/query', {outTradeNo})
                .then(res => {
                    if (res.code === 200) {
                        const payResult = res.data
                        console.log('查询支付状态成功', payResult)
                        resolve(payResult)
                    } else {
                        reject(new Error(res.msg || '查询支付状态失败'))
                    }
                })
                .catch(err => {
                    console.error('查询支付状态异常', err)
                    reject(err)
                })
        })
    }

    /**
     * 轮询查询支付状态
     * @param {string} outTradeNo 商户订单号
     * @param {number} maxRetries 最大重试次数，默认10次
     * @param {number} interval 查询间隔（毫秒），默认2秒
     * @returns {Promise}
     */
    static pollPaymentStatus(outTradeNo, maxRetries = 10, interval = 2000) {
        return new Promise((resolve, reject) => {
            let retryCount = 0

            const poll = () => {
                PaymentUtils.queryPaymentStatus(outTradeNo)
                    .then(result => {
                        if (result.tradeState === 'SUCCESS') {
                            // 支付成功
                            resolve(result)
                        } else if (result.tradeState === 'CLOSED' || result.tradeState === 'REVOKED') {
                            // 订单已关闭或已撤销
                            reject(new Error('订单已关闭'))
                        } else if (retryCount >= maxRetries) {
                            // 达到最大重试次数
                            reject(new Error('查询超时'))
                        } else {
                            // 继续轮询
                            retryCount++
                            setTimeout(poll, interval)
                        }
                    })
                    .catch(err => {
                        if (retryCount >= maxRetries) {
                            reject(err)
                        } else {
                            retryCount++
                            setTimeout(poll, interval)
                        }
                    })
            }

            poll()
        })
    }

    /**
     * 生成订单号
     * @param {string} prefix 前缀，默认'PAY'
     * @returns {string}
     */
    static generateOrderNo(prefix = 'PAY') {
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')
        const hour = String(now.getHours()).padStart(2, '0')
        const minute = String(now.getMinutes()).padStart(2, '0')
        const second = String(now.getSeconds()).padStart(2, '0')
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')

        return `${prefix}${year}${month}${day}${hour}${minute}${second}${random}`
    }

    /**
     * 格式化金额（元转分）
     * @param {number} yuan 金额（元）
     * @returns {number} 金额（分）
     */
    static yuanToFen(yuan) {
        return Math.round(yuan * 100)
    }

    /**
     * 格式化金额（分转元）
     * @param {number} fen 金额（分）
     * @returns {number} 金额（元）
     */
    static fenToYuan(fen) {
        return fen / 100
    }
}

module.exports = PaymentUtils
