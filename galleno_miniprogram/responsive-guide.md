# 小程序自适应设计指南

## 🎯 自适应设计目标

确保小程序在不同设备和屏幕尺寸下都能提供良好的用户体验：
- iPhone SE (375px)
- iPhone 6/7/8 (375px)
- iPhone 6/7/8 Plus (414px)
- iPhone X/11/12 (375px)
- iPhone 12 Pro Max (428px)
- 各种Android设备

## 📐 响应式断点

### 小屏设备 (< 375px)
- 减少内边距和外边距
- 调整字体大小
- 简化布局元素
- 优化触摸目标大小

### 标准屏幕 (375px - 414px)
- 默认设计基准
- 平衡的间距和字体
- 完整的功能展示

### 大屏设备 (> 414px)
- 增加内容区域宽度
- 更大的字体和间距
- 更丰富的视觉效果

## 🛠️ 技术实现

### 1. 全局响应式工具类

```css
/* 布局工具类 */
.flex { display: flex; }
.flex-column { flex-direction: column; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }

/* 响应式网格 */
.grid { display: grid; gap: 32rpx; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* 响应式间距 */
.p-16 { padding: 16rpx; }
.p-32 { padding: 32rpx; }
.mt-32 { margin-top: 32rpx; }
```

### 2. 媒体查询适配

```css
/* 小屏适配 */
@media (max-width: 374px) {
  .container { padding: 0 24rpx; }
  .text-xl { font-size: 32rpx; }
}

/* 大屏适配 */
@media (min-width: 415px) {
  .container { max-width: 750rpx; margin: 0 auto; }
  .text-xl { font-size: 40rpx; }
}

/* 横屏适配 */
@media (orientation: landscape) {
  .banner-content { flex-direction: row; }
}
```

### 3. JavaScript响应式工具

```javascript
const util = require('../../utils/util.js')

// 获取屏幕信息
const screenWidth = util.getScreenWidth()
const isSmallScreen = util.isSmallScreen()
const isLargeScreen = util.isLargeScreen()

// 单位转换
const pxValue = util.rpxToPx(750) // 750rpx转px
const rpxValue = util.pxToRpx(375) // 375px转rpx

// 自适应字体
const fontSize = util.getAdaptiveFontSize(28)
```

## 📱 页面自适应特性

### 会议列表页
- ✅ 响应式卡片布局
- ✅ 自适应筛选标签
- ✅ 小屏文本简化
- ✅ 大屏增强显示

### 酒店预订页
- ✅ 响应式房型列表
- ✅ 自适应日期选择
- ✅ 小屏垂直布局
- ✅ 横屏优化显示

### 个人中心页
- ✅ 响应式统计网格
- ✅ 自适应菜单布局
- ✅ 小屏简化显示
- ✅ 大屏增强功能

### 预订流程页
- ✅ 响应式表单布局
- ✅ 自适应按钮尺寸
- ✅ 小屏优化输入
- ✅ 大屏增强体验

## 🎨 设计原则

### 1. 移动优先
- 从小屏设计开始
- 逐步增强到大屏
- 确保核心功能可用

### 2. 触摸友好
- 最小触摸目标 44rpx
- 合适的间距避免误触
- 清晰的视觉反馈

### 3. 内容优先
- 重要信息优先显示
- 小屏时隐藏次要元素
- 保持信息层次清晰

### 4. 性能优化
- 使用rpx单位减少计算
- 避免复杂的媒体查询
- 优化图片和资源加载

## 🧪 测试方法

### 1. 开发者工具测试
- 使用不同设备模拟器
- 测试横竖屏切换
- 检查各种分辨率

### 2. 真机测试
- iPhone SE (小屏)
- iPhone 11 (标准屏)
- iPhone 12 Pro Max (大屏)
- 各种Android设备

### 3. 功能测试
- 所有按钮可点击
- 文本清晰可读
- 布局不重叠
- 滚动流畅

## 📋 自适应检查清单

### 布局检查
- [ ] 容器宽度自适应
- [ ] 内容不超出屏幕
- [ ] 间距比例协调
- [ ] 网格布局响应式

### 文本检查
- [ ] 字体大小适中
- [ ] 行高合适
- [ ] 文本不截断
- [ ] 重要信息突出

### 交互检查
- [ ] 按钮大小合适
- [ ] 触摸目标足够大
- [ ] 反馈及时清晰
- [ ] 操作流程顺畅

### 图片检查
- [ ] 图片比例正确
- [ ] 加载速度快
- [ ] 清晰度适中
- [ ] 占位符合理

## 🔧 常见问题解决

### 1. 文本溢出
```css
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
```

### 2. 图片适配
```css
.responsive-image {
  width: 100%;
  height: auto;
  max-width: 100%;
}
```

### 3. 弹性布局
```css
.flex-container {
  display: flex;
  flex-wrap: wrap;
  gap: 32rpx;
}

.flex-item {
  flex: 1;
  min-width: 200rpx;
}
```

## 🚀 性能优化建议

1. **使用rpx单位**: 自动适配不同屏幕密度
2. **避免固定尺寸**: 使用相对单位和百分比
3. **优化图片**: 使用合适的分辨率和格式
4. **减少重排**: 避免频繁的DOM操作
5. **懒加载**: 图片和内容按需加载

## 📈 未来改进

- [ ] 添加更多设备适配
- [ ] 优化动画性能
- [ ] 增强无障碍支持
- [ ] 支持深色模式
- [ ] 添加手势操作
