package com.ruoyi.web.controller.api;

import java.util.List;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.User;
import com.ruoyi.common.core.domain.WechatPayParams;
import com.ruoyi.common.core.domain.WechatPayV3Result;
import com.ruoyi.common.core.domain.request.wechat.WechatPayRequest;
import com.ruoyi.common.core.domain.request.hotel.CreateOrderRequest;
import com.ruoyi.system.domain.HotelOrder;
import com.ruoyi.system.service.IHotelOrderService;
import java.math.BigDecimal;
import com.ruoyi.system.domain.Conference;
import com.ruoyi.system.domain.Room;
import com.ruoyi.system.domain.CategoryCode;
import com.ruoyi.system.service.IConferenceService;
import com.ruoyi.system.service.IRoomService;
import com.ruoyi.system.service.ICategoryCodeService;
import com.ruoyi.system.service.ICategoryCodeRoomService;
import com.ruoyi.system.service.IWechatPayService;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.common.utils.StringUtils;

/**
 * 酒店预定小程序API接口
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@RestController
@RequestMapping("/api/hotel")
public class HotelApiController {
    private static final Logger log = LoggerFactory.getLogger(HotelApiController.class);
    @Autowired
    private IConferenceService conferenceService;

    @Autowired
    private IRoomService roomService;

    @Autowired
    private ICategoryCodeService categoryCodeService;

    @Autowired
    private ICategoryCodeRoomService categoryCodeRoomService;

    @Autowired
    private IWechatPayService wechatPayService;

    @Autowired
    private IHotelOrderService hotelOrderService;

    /**
     * 获取当前登录的小程序用户
     */
    private User getCurrentUser() {
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            Object principal = subject.getPrincipal();
            if (principal instanceof User) {
                return (User) principal;
            }
        }
        return null;
    }

    /**
     * 获取启用的会议列表（小程序首页）
     */
    @GetMapping("/conferences")
    public AjaxResult getConferences() {
        List<Conference> conferences = conferenceService.selectEnabledConferenceList();
        return AjaxResult.success(conferences);
    }

    /**
     * 获取会议详情
     */
    @GetMapping("/conference")
    public AjaxResult getConference(@RequestParam Long id) {
        Conference conference = conferenceService.selectConferenceById(id);
        if (conference == null) {
            return AjaxResult.error("会议不存在");
        }
        if (!conference.enable()) {
            return AjaxResult.error("会议未启用");
        }
        return AjaxResult.success(conference);
    }

    /**
     * 验证会议识别码
     */
    @PostMapping("/validateCode")
    public AjaxResult validateCode(@RequestBody @Validated ValidateCodeRequest request) {
        String categoryId = request.getCategoryId();
        Long conferenceId = request.getConferenceId();
        // 获取当前登录用户（Shiro已自动验证token）
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return AjaxResult.error("用户未登录");
        }

        // 验证会议是否存在且启用
        Conference conference = conferenceService.selectConferenceById(conferenceId);
        if (conference == null || !conference.enable()) {
            return AjaxResult.error("会议不存在或未启用");
        }

        // 验证识别码
        CategoryCode categoryCode = categoryCodeService.selectCategoryCodeById(categoryId, conferenceId);
        if (categoryCode == null) {
            return AjaxResult.error("识别码不正确");
        }

        // 记录用户操作日志
        System.out.println("用户 " + currentUser.getNickName() + "(" + currentUser.getOpenid() + ") 验证识别码：" + categoryId);

        return AjaxResult.success("验证成功", categoryCode);
    }



    /**
     * 获取订单支付参数（用于重新调起支付）
     */
    @GetMapping("/order/payment-params")
    public AjaxResult getOrderPaymentParams(@RequestParam String orderNo) {
        try {
            // 获取当前登录用户
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            log.info("用户 {} 获取订单支付参数，订单号: {}", currentUser.getNickName(), orderNo);

            // 查询订单
            HotelOrder order = hotelOrderService.selectHotelOrderByOrderNo(orderNo);
            if (order == null) {
                return AjaxResult.error("订单不存在");
            }

            // 验证订单归属
            if (!currentUser.getOpenid().equals(order.getOpenid())) {
                return AjaxResult.error("无权限操作此订单");
            }

            // 验证订单状态
            if (!HotelOrder.OrderStatus.PENDING.equals(order.getOrderStatus())) {
                return AjaxResult.error("订单状态不正确，无法支付");
            }

            // 检查prepay_id是否存在
            if (StringUtils.isEmpty(order.getPrepayId())) {
                return AjaxResult.error("订单支付信息不存在，请重新下单");
            }

            // 使用已有的prepay_id重新生成支付参数
            WechatPayParams payParams = wechatPayService.regeneratePayParams(order.getPrepayId());

            if (payParams == null) {
                return AjaxResult.error("生成支付参数失败，prepay_id可能已过期");
            }

            log.info("获取订单支付参数成功，订单号: {}", orderNo);
            return AjaxResult.success("获取支付参数成功", payParams);

        } catch (Exception e) {
            log.error("获取订单支付参数异常", e);
            return AjaxResult.error("获取支付参数异常：" + e.getMessage());
        }
    }

    /**
     * 查询支付订单状态
     */
    @GetMapping("/payment/query")
    public AjaxResult queryPayment(@RequestParam String outTradeNo) {
        try {
            // 获取当前登录用户
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            log.info("用户 {} 查询支付订单状态，订单号: {}",
                currentUser.getNickName(), outTradeNo);

            // 查询支付订单状态（V3版本）
            WechatPayV3Result result = wechatPayService.queryPayOrder(outTradeNo);

            if (result == null) {
                return AjaxResult.error("查询支付订单失败");
            }

            return AjaxResult.success("查询支付订单成功", result);
        } catch (Exception e) {
            log.error("查询支付订单异常", e);
            return AjaxResult.error("查询支付订单异常：" + e.getMessage());
        }
    }

    public static class ValidateCodeRequest {
        @NotBlank
        private String categoryId;
        @NotNull
        private Long conferenceId;

        public String getCategoryId() {
            return categoryId;
        }

        public void setCategoryId(String categoryId) {
            this.categoryId = categoryId;
        }

        public Long getConferenceId() {
            return conferenceId;
        }

        public void setConferenceId(Long conferenceId) {
            this.conferenceId = conferenceId;
        }
    }

    // ==================== 订单管理接口 ====================

    /**
     * 创建酒店订单并发起支付
     */
    @PostMapping("/order/create")
    public AjaxResult createOrder(@RequestBody @Validated CreateOrderRequest createOrderRequest)
    {
        try {
            // 获取当前登录用户
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 设置用户openid
            createOrderRequest.setOpenid(currentUser.getOpenid());

            log.info("用户 {} 创建酒店订单，会议ID: {}, 房间ID: {}, 金额: {}",
                currentUser.getNickName(), createOrderRequest.getConferenceId(),
                createOrderRequest.getRoomId(), createOrderRequest.getTotalAmount());

            // 1. 获取房间总库存
            int totalInventory = categoryCodeRoomService.getRoomInventory(
                createOrderRequest.getRoomId(), createOrderRequest.getConferenceId());

            // 2. 构建订单对象
            HotelOrder hotelOrder = buildHotelOrder(createOrderRequest, currentUser);

            // 3. 使用带锁的创建订单方法（包含库存校验和并发控制）
            IHotelOrderService.OrderCreationResult creationResult =
                hotelOrderService.createOrderWithInventoryCheck(
                    hotelOrder,
                    totalInventory,
                    createOrderRequest.getCheckinDate(),
                    createOrderRequest.getCheckoutDate());

            if (!creationResult.isSuccess()) {
                return AjaxResult.error(creationResult.getMessage());
            }

            // 获取创建成功的订单
            hotelOrder = creationResult.getHotelOrder();
            log.info("房间库存校验通过，订单创建成功，房间ID: {}, 剩余数量: {}",
                createOrderRequest.getRoomId(), creationResult.getAvailableCount());

            // 4. 构建微信支付请求
            WechatPayRequest payRequest = buildWechatPayRequest(hotelOrder, createOrderRequest);

            // 5. 创建微信支付订单
            WechatPayParams payParams = wechatPayService.createPayOrder(payRequest);

            if (payParams == null) {
                return AjaxResult.error("创建支付订单失败");
            }

            // 6. 更新订单的prepay_id
            hotelOrder.setPrepayId(extractPrepayId(payParams.getPackageValue()));
            hotelOrderService.updateHotelOrder(hotelOrder);

            log.info("创建酒店订单成功，订单号: {}, 订单ID: {}", hotelOrder.getOrderNo(), hotelOrder.getOrderId());
            payParams.setOrderId(hotelOrder.getOrderId());
            payParams.setOrderNo(hotelOrder.getOrderNo());
            // 返回支付参数和订单信息
            return AjaxResult.success("创建订单成功", payParams);
        } catch (Exception e) {
            log.error("创建酒店订单异常", e);
            return AjaxResult.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 查询用户订单列表
     */
    @GetMapping("/order/list")
    public AjaxResult getOrderList(@RequestParam(required = false) String orderStatus)
    {
        try {
            // 获取当前登录用户
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            log.info("用户 {} 查询订单列表，状态: {}", currentUser.getNickName(), orderStatus);

            // 构建查询条件
            HotelOrder queryOrder = new HotelOrder();
            queryOrder.setOpenid(currentUser.getOpenid());
            if (StringUtils.isNotEmpty(orderStatus)) {
                queryOrder.setOrderStatus(orderStatus);
            }

            List<HotelOrder> orderList = hotelOrderService.selectHotelOrderList(queryOrder);

            // 过滤掉超过30分钟未支付的订单（不展示给用户）
            List<HotelOrder> filteredOrderList = orderList.stream()
                .filter(order -> {
                    // 如果不是待支付状态，直接显示
                    if (!"PENDING".equals(order.getOrderStatus())) {
                        return true;
                    }

                    // 如果是待支付状态，检查是否超过30分钟
                    if (order.getCreateTime() != null) {
                        long createTimeMillis = order.getCreateTime().getTime();
                        long currentTimeMillis = System.currentTimeMillis();
                        long diffMinutes = (currentTimeMillis - createTimeMillis) / (1000 * 60);

                        // 超过30分钟的待支付订单不显示
                        return diffMinutes <= 30;
                    }

                    // 如果没有创建时间，为了安全起见不显示
                    return false;
                })
                .collect(java.util.stream.Collectors.toList());

            return AjaxResult.success("查询成功", filteredOrderList);
        } catch (Exception e) {
            log.error("查询订单列表异常", e);
            return AjaxResult.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 查询订单详情
     */
    @GetMapping("/order/detail/{orderNo}")
    public AjaxResult getOrderDetail(@PathVariable String orderNo)
    {
        try {
            // 获取当前登录用户
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            log.info("用户 {} 查询订单详情，订单号: {}", currentUser.getNickName(), orderNo);

            HotelOrder order = hotelOrderService.selectHotelOrderByOrderNo(orderNo);

            if (order == null) {
                return AjaxResult.error("订单不存在");
            }

            // 验证订单归属
            if (!currentUser.getOpenid().equals(order.getOpenid())) {
                return AjaxResult.error("无权限查看此订单");
            }

            return AjaxResult.success("查询成功", order);
        } catch (Exception e) {
            log.error("查询订单详情异常", e);
            return AjaxResult.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    @PostMapping("/order/cancel/{orderNo}")
    public AjaxResult cancelOrder(@PathVariable String orderNo, @RequestParam(required = false) String cancelReason)
    {
        try {
            // 获取当前登录用户
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            log.info("用户 {} 取消订单，订单号: {}, 取消原因: {}",
                currentUser.getNickName(), orderNo, cancelReason);

            HotelOrder order = hotelOrderService.selectHotelOrderByOrderNo(orderNo);

            if (order == null) {
                return AjaxResult.error("订单不存在");
            }

            // 验证订单归属
            if (!currentUser.getOpenid().equals(order.getOpenid())) {
                return AjaxResult.error("无权限操作此订单");
            }

            // 验证订单状态
            if (!HotelOrder.OrderStatus.PENDING.equals(order.getOrderStatus())) {
                return AjaxResult.error("只能取消待支付的订单");
            }

            String reason = StringUtils.isNotEmpty(cancelReason) ? cancelReason : "用户主动取消";
            int result = hotelOrderService.cancelOrder(orderNo, reason, currentUser.getNickName());

            if (result > 0) {
                return AjaxResult.success("取消订单成功");
            } else {
                return AjaxResult.error("取消订单失败");
            }
        } catch (Exception e) {
            log.error("取消订单异常", e);
            return AjaxResult.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 构建酒店订单对象
     */
    private HotelOrder buildHotelOrder(CreateOrderRequest request, User currentUser)
    {
        HotelOrder hotelOrder = new HotelOrder();

        // 基本信息
        hotelOrder.setUserId(currentUser.getId());
        hotelOrder.setOpenid(request.getOpenid());
        hotelOrder.setConferenceId(request.getConferenceId());
        hotelOrder.setRoomId(request.getRoomId());
        hotelOrder.setRoomType(request.getRoomType());
        hotelOrder.setRoomName(request.getRoomName());

        // 时间信息
        hotelOrder.setCheckinDate(request.getCheckinDate());
        hotelOrder.setCheckoutDate(request.getCheckoutDate());
        hotelOrder.setNights(request.getNights());

        // 金额信息
        hotelOrder.setRoomPrice(request.getRoomPrice());
//        hotelOrder.setTotalAmount(request.getTotalAmount());
        hotelOrder.setTotalAmount(request.getDepositAmount());
        hotelOrder.setDepositAmount(request.getDepositAmount());

        // 入住人信息
        hotelOrder.setGuestName(request.getGuestName());
        hotelOrder.setGuestPhone(request.getGuestPhone());
        hotelOrder.setGuestIdCard(request.getGuestIdCard());
        hotelOrder.setRemark(request.getRemark());

        // 状态信息
        hotelOrder.setOrderStatus(HotelOrder.OrderStatus.PENDING);
        hotelOrder.setPaymentStatus(HotelOrder.PaymentStatus.UNPAID);

        // 创建信息
        hotelOrder.setCreateBy(currentUser.getNickName());

        return hotelOrder;
    }

    /**
     * 构建微信支付请求
     */
    private WechatPayRequest buildWechatPayRequest(HotelOrder hotelOrder, CreateOrderRequest request)
    {
        WechatPayRequest payRequest = new WechatPayRequest();

        payRequest.setOutTradeNo(hotelOrder.getOrderNo());
        payRequest.setBody("酒店预订-" + hotelOrder.getRoomName());
        payRequest.setDetail(String.format("%s，%s至%s，共%d晚",
            hotelOrder.getRoomName(),
            new java.text.SimpleDateFormat("MM-dd").format(hotelOrder.getCheckinDate()),
            new java.text.SimpleDateFormat("MM-dd").format(hotelOrder.getCheckoutDate()),
            hotelOrder.getNights()));

        // 金额转换为分
        payRequest.setTotalFee(hotelOrder.getDepositAmount().multiply(new BigDecimal("100")).intValue());
        payRequest.setOpenid(hotelOrder.getOpenid());
        payRequest.setSpbillCreateIp("127.0.0.1");
        payRequest.setAttach(String.format("conferenceId:%d,roomId:%d",
            hotelOrder.getConferenceId(), hotelOrder.getRoomId()));

        return payRequest;
    }

    /**
     * 从package值中提取prepay_id
     */
    private String extractPrepayId(String packageValue)
    {
        if (StringUtils.isNotEmpty(packageValue) && packageValue.startsWith("prepay_id=")) {
            return packageValue.substring("prepay_id=".length());
        }
        return null;
    }
}
