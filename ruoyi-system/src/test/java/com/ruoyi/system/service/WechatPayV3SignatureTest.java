package com.ruoyi.system.service;

import com.ruoyi.common.utils.wechat.WechatPayV3Utils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 微信支付V3版本签名验证测试
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WechatPayV3SignatureTest {

    @Autowired
    private WechatPayV3Utils wechatPayV3Utils;

    /**
     * 测试回调验签功能
     */
    @Test
    public void testVerifyNotifySignature() {
        // 模拟微信回调数据
        String timestamp = "1642410000";
        String nonce = "5K8264ILTKCH16CQ2502SI8ZNMTM67VS";
        String body = "{\"id\":\"EV-2018022511223320873\",\"create_time\":\"2025-01-17T15:45:00+08:00\",\"resource_type\":\"encrypt-resource\",\"event_type\":\"TRANSACTION.SUCCESS\"}";
        String signature = "test_signature_base64_encoded";
        String serialNumber = "1B0CE287AD107AB325429D20A2EE26C94E9B69F5";

        // 执行验签（开发环境会自动跳过）
        boolean result = wechatPayV3Utils.verifyNotifySignature(timestamp, nonce, body, signature, serialNumber);

        System.out.println("验签结果: " + (result ? "成功" : "失败"));
        
        // 在开发环境下应该返回true（跳过验签）
        // 在生产环境下需要配置真实的微信平台证书
    }

    /**
     * 测试验签参数验证
     */
    @Test
    public void testVerifySignatureWithInvalidParams() {
        // 测试空参数
        boolean result1 = wechatPayV3Utils.verifyNotifySignature(null, "nonce", "body", "signature", "serial");
        System.out.println("空timestamp验签结果: " + result1);

        boolean result2 = wechatPayV3Utils.verifyNotifySignature("timestamp", null, "body", "signature", "serial");
        System.out.println("空nonce验签结果: " + result2);

        boolean result3 = wechatPayV3Utils.verifyNotifySignature("timestamp", "nonce", null, "signature", "serial");
        System.out.println("空body验签结果: " + result3);

        boolean result4 = wechatPayV3Utils.verifyNotifySignature("timestamp", "nonce", "body", null, "serial");
        System.out.println("空signature验签结果: " + result4);

        boolean result5 = wechatPayV3Utils.verifyNotifySignature("timestamp", "nonce", "body", "signature", null);
        System.out.println("空serialNumber验签结果: " + result5);
    }

    /**
     * 测试公钥设置功能
     */
    @Test
    public void testSetPublicKey() {
        String publicKeyString = "-----BEGIN PUBLIC KEY-----\nTEST_PUBLIC_KEY_CONTENT\n-----END PUBLIC KEY-----";

        try {
            // 测试动态设置公钥（这个会失败，因为公钥内容是假的）
            wechatPayV3Utils.setWechatPlatformPublicKey(publicKeyString);
            System.out.println("公钥设置测试完成");
        } catch (Exception e) {
            System.out.println("公钥设置测试异常（预期的）: " + e.getMessage());
        }
    }

    /**
     * 测试公钥重新加载功能
     */
    @Test
    public void testReloadPublicKey() {
        try {
            // 测试重新加载公钥
            wechatPayV3Utils.reloadWechatPlatformPublicKey();
            System.out.println("公钥重新加载测试完成");
        } catch (Exception e) {
            System.out.println("公钥重新加载测试异常: " + e.getMessage());
        }
    }
}
