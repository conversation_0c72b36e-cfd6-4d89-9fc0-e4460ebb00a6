// test-login-flow.js - 测试修复后的登录流程
// 在微信开发者工具控制台中使用

/**
 * 测试登录流程的完整性
 */

// 1. 模拟未登录状态
function simulateLoggedOut() {
  console.log('=== 模拟未登录状态 ===');
  
  // 清除登录信息
  wx.removeStorageSync('token');
  wx.removeStorageSync('userInfo');
  
  console.log('已清除登录信息');
  
  // 检查清除结果
  const auth = require('./utils/auth.js');
  console.log('当前登录状态:', auth.isLoggedIn());
}

// 2. 测试自动登录流程
function testAutoLoginFlow() {
  console.log('=== 测试自动登录流程 ===');
  
  // 获取当前页面实例
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  
  if (currentPage.route !== 'pages/conference-list/conference-list') {
    console.log('请在会议列表页面运行此测试');
    return;
  }
  
  // 重置页面状态
  currentPage.setData({
    autoLoginAttempted: false,
    isLoggedIn: false,
    conferences: [],
    filteredConferences: []
  });
  
  console.log('开始测试自动登录流程...');
  
  // 调用自动登录方法
  currentPage.performAutoLogin();
}

// 3. 测试带认证的数据加载
function testLoadConferencesWithAuth() {
  console.log('=== 测试带认证的数据加载 ===');
  
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  
  if (currentPage.route !== 'pages/conference-list/conference-list') {
    console.log('请在会议列表页面运行此测试');
    return;
  }
  
  console.log('开始测试带认证的数据加载...');
  currentPage.loadConferencesWithAuth();
}

// 4. 测试API调用顺序
function testApiCallOrder() {
  console.log('=== 测试API调用顺序 ===');
  
  // 监听网络请求
  const originalRequest = wx.request;
  const apiCalls = [];
  
  wx.request = function(options) {
    apiCalls.push({
      url: options.url,
      method: options.method || 'GET',
      timestamp: Date.now()
    });
    
    console.log('API调用:', options.url);
    
    // 调用原始方法
    return originalRequest.call(this, options);
  };
  
  // 清除登录状态
  simulateLoggedOut();
  
  // 等待一段时间后检查API调用顺序
  setTimeout(() => {
    console.log('API调用顺序:', apiCalls);
    
    // 恢复原始request方法
    wx.request = originalRequest;
    
    // 分析调用顺序
    const loginCalls = apiCalls.filter(call => call.url.includes('/wechat/login'));
    const conferenceCalls = apiCalls.filter(call => call.url.includes('/hotel/conferences'));
    
    console.log('登录接口调用次数:', loginCalls.length);
    console.log('会议列表接口调用次数:', conferenceCalls.length);
    
    if (conferenceCalls.length > 0 && loginCalls.length > 0) {
      const firstLogin = loginCalls[0];
      const firstConference = conferenceCalls[0];
      
      if (firstLogin.timestamp < firstConference.timestamp) {
        console.log('✅ 调用顺序正确：先登录，后获取会议列表');
      } else {
        console.log('❌ 调用顺序错误：先获取会议列表，后登录');
      }
    }
  }, 5000);
  
  // 触发自动登录流程
  testAutoLoginFlow();
}

// 5. 完整流程测试
function runCompleteTest() {
  console.log('=== 开始完整流程测试 ===');
  
  console.log('步骤1: 清除登录状态');
  simulateLoggedOut();
  
  setTimeout(() => {
    console.log('步骤2: 测试API调用顺序');
    testApiCallOrder();
  }, 1000);
}

// 6. 检查页面状态
function checkPageState() {
  console.log('=== 检查页面状态 ===');
  
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  
  if (currentPage.route !== 'pages/conference-list/conference-list') {
    console.log('当前不在会议列表页面');
    return;
  }
  
  const pageData = currentPage.data;
  console.log('页面状态:', {
    isLoggedIn: pageData.isLoggedIn,
    autoLoginAttempted: pageData.autoLoginAttempted,
    conferenceCount: pageData.conferences.length,
    filteredConferenceCount: pageData.filteredConferences.length,
    loading: pageData.loading
  });
  
  const auth = require('./utils/auth.js');
  console.log('认证状态:', {
    isLoggedIn: auth.isLoggedIn(),
    hasToken: !!auth.getToken(),
    hasUserInfo: !!auth.getCurrentUser().nickName
  });
}

// 导出测试函数
module.exports = {
  simulateLoggedOut,
  testAutoLoginFlow,
  testLoadConferencesWithAuth,
  testApiCallOrder,
  runCompleteTest,
  checkPageState
};

// 使用说明
console.log(`
=== 登录流程测试脚本使用说明 ===

在微信开发者工具控制台中运行以下命令：

1. 加载测试脚本：
   const test = require('./test-login-flow.js');

2. 检查页面状态：
   test.checkPageState();

3. 模拟未登录状态：
   test.simulateLoggedOut();

4. 测试自动登录流程：
   test.testAutoLoginFlow();

5. 测试带认证的数据加载：
   test.testLoadConferencesWithAuth();

6. 测试API调用顺序：
   test.testApiCallOrder();

7. 运行完整测试：
   test.runCompleteTest();

注意：
- 请在会议列表页面运行测试
- 确保后端服务正在运行
- 观察控制台输出和网络请求顺序
`);
