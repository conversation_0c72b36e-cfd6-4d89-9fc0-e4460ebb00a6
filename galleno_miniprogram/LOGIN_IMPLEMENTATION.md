# 小程序登录功能实现说明

## 概述

本项目已实现完整的微信小程序登录功能，包括前端登录流程和与后端API的集成。

## 实现的功能

### 1. 核心文件

- **`utils/api.js`** - 网络请求封装，处理与后端API的交互
- **`utils/auth.js`** - 认证管理器，提供完整的登录/登出功能
- **`pages/profile/profile.js`** - 个人中心页面，展示登录状态和用户信息

### 2. 登录流程

1. **用户点击登录按钮**
2. **调用 `wx.login()` 获取 `jsCode`**
3. **发送 `jsCode` 到后端 `/api/login` 接口**
4. **后端返回用户信息和token**
5. **保存token和用户信息到本地存储**
6. **更新UI显示登录状态**

### 3. API接口对接

#### 登录接口
```javascript
// POST /api/login
{
  "jsCode": "微信登录凭证"
}

// 响应格式（基于AjaxResult）
{
  "code": 0,
  "msg": "登录成功",
  "data": {
    "isNewUser": true,
    "user": {
      "nickName": "用户昵称",
      "avatarUrl": "头像URL",
      "openId": "微信openId",
      "unionId": "微信unionId",
      "memberLevel": "普通会员"
    },
    "token": "JWT令牌"
  }
}
```

#### 认证过滤器
- 使用 `MiniAppAuthFilter` 进行请求认证
- 支持 `Authorization: Bearer <token>` 请求头
- 支持URL参数 `?token=<token>`
- 401状态码时自动清除本地登录状态

### 4. 使用方法

#### 基本登录
```javascript
const auth = require('../../utils/auth.js')

// 登录
auth.login({
  success: (result) => {
    console.log('登录成功:', result)
    // 处理登录成功逻辑
  },
  fail: (err) => {
    console.error('登录失败:', err)
    // 处理登录失败逻辑
  }
})
```

#### 检查登录状态
```javascript
// 检查是否已登录
if (auth.isLoggedIn()) {
  console.log('用户已登录')
  const userInfo = auth.getCurrentUser()
  console.log('用户信息:', userInfo)
}
```

#### 确保用户已登录
```javascript
// 如果未登录会自动引导用户登录
auth.ensureLogin()
  .then(userInfo => {
    console.log('用户已登录:', userInfo)
    // 执行需要登录的操作
  })
  .catch(err => {
    console.log('用户取消登录:', err)
  })
```

#### 退出登录
```javascript
auth.logout({
  success: () => {
    console.log('退出登录成功')
    // 更新UI状态
  }
})
```

### 5. 配置说明

#### API配置 (`utils/api.js`)
```javascript
const API_CONFIG = {
  dev: {
    baseUrl: 'http://localhost:8080/api',  // 开发环境
    timeout: 10000
  },
  prod: {
    baseUrl: 'https://your-domain.com/api', // 生产环境
    timeout: 10000
  }
}
```

#### 环境切换
修改 `utils/api.js` 中的 `ENV` 变量：
```javascript
const ENV = 'dev' // 开发环境
// const ENV = 'prod' // 生产环境
```

### 6. 错误处理

- **网络错误**: 自动显示网络异常提示
- **401未授权**: 自动清除登录状态，引导重新登录
- **登录失败**: 显示具体错误信息
- **Token过期**: 自动检测并处理

### 7. 本地存储

- **`token`**: JWT认证令牌
- **`userInfo`**: 用户基本信息

### 8. 安全特性

- Token自动添加到请求头
- 401状态码自动处理
- 登录状态自动检查
- 敏感信息本地加密存储（可扩展）

## 后端要求

### 1. 登录接口实现
```java
@PostMapping("/login")
public AjaxResult login(@RequestParam String jsCode) {
    // 实现微信小程序登录逻辑
    MiniAppLoginResult result = miniAppLoginService.login(jsCode);
    return AjaxResult.success("登录成功", result);
}
```

### 2. 认证过滤器配置
确保 `MiniAppAuthFilter` 正确配置在需要认证的接口上。

### 3. 响应格式
所有API响应必须遵循 `AjaxResult` 格式：
- `code: 0` - 成功
- `code: 301` - 警告
- `code: 500` - 错误

## 测试建议

1. **登录流程测试**: 测试完整的登录流程
2. **Token有效性**: 测试token过期处理
3. **网络异常**: 测试网络断开情况
4. **并发登录**: 测试重复点击登录按钮
5. **退出登录**: 测试登出后的状态清理

## 注意事项

1. **开发环境**: 确保后端服务正常运行
2. **网络配置**: 小程序需要配置合法域名
3. **HTTPS要求**: 生产环境必须使用HTTPS
4. **微信开发者工具**: 建议使用最新版本进行调试

## 扩展功能

- 自动登录（静默登录）
- 登录状态持久化
- 多设备登录管理
- 用户信息同步更新
- 登录日志记录
