// test-auto-login.js - 自动登录功能测试脚本
// 这个文件用于在开发者工具控制台中测试自动登录功能

/**
 * 测试自动登录功能
 * 在微信开发者工具的控制台中运行以下代码
 */

// 1. 清除登录状态测试
function clearLoginState() {
  console.log('=== 清除登录状态测试 ===');
  wx.removeStorageSync('token');
  wx.removeStorageSync('userInfo');
  console.log('已清除token和userInfo');
  
  // 检查清除结果
  const token = wx.getStorageSync('token');
  const userInfo = wx.getStorageSync('userInfo');
  console.log('清除后状态:', { token, userInfo });
}

// 2. 检查登录状态
function checkLoginState() {
  console.log('=== 检查登录状态 ===');
  const auth = require('./utils/auth.js');
  const isLoggedIn = auth.isLoggedIn();
  const currentUser = auth.getCurrentUser();
  const token = auth.getToken();
  
  console.log('登录状态:', {
    isLoggedIn,
    currentUser,
    token: token ? token.substring(0, 20) + '...' : '无'
  });
}

// 3. 测试自动登录
function testAutoLogin() {
  console.log('=== 测试自动登录 ===');
  const auth = require('./utils/auth.js');
  
  auth.autoLogin()
    .then(result => {
      console.log('自动登录成功:', result);
      checkLoginState();
    })
    .catch(err => {
      console.log('自动登录失败:', err);
      checkLoginState();
    });
}

// 4. 测试token验证
function testTokenValidation() {
  console.log('=== 测试token验证 ===');
  const auth = require('./utils/auth.js');
  
  if (!auth.isLoggedIn()) {
    console.log('用户未登录，无法测试token验证');
    return;
  }
  
  auth.checkTokenValid()
    .then(userInfo => {
      console.log('Token验证成功，用户信息:', userInfo);
    })
    .catch(err => {
      console.log('Token验证失败:', err);
    });
}

// 5. 完整测试流程
function runFullTest() {
  console.log('=== 开始完整测试流程 ===');
  
  // 步骤1：清除登录状态
  clearLoginState();
  
  // 步骤2：检查清除后的状态
  setTimeout(() => {
    checkLoginState();
    
    // 步骤3：测试自动登录
    setTimeout(() => {
      testAutoLogin();
      
      // 步骤4：等待登录完成后测试token验证
      setTimeout(() => {
        testTokenValidation();
      }, 3000);
    }, 1000);
  }, 500);
}

// 导出测试函数
module.exports = {
  clearLoginState,
  checkLoginState,
  testAutoLogin,
  testTokenValidation,
  runFullTest
};

// 使用说明
console.log(`
=== 自动登录测试脚本使用说明 ===

在微信开发者工具控制台中运行以下命令：

1. 加载测试脚本：
   const test = require('./test-auto-login.js');

2. 清除登录状态：
   test.clearLoginState();

3. 检查登录状态：
   test.checkLoginState();

4. 测试自动登录：
   test.testAutoLogin();

5. 测试token验证：
   test.testTokenValidation();

6. 运行完整测试：
   test.runFullTest();

注意：
- 确保后端服务正在运行
- 确保网络连接正常
- 观察控制台输出的日志信息
`);
