<!--input-test.wxml-->
<view class="container">
  <view class="test-section">
    <view class="section-title">输入框测试页面</view>
    
    <view class="form-group">
      <text class="form-label">姓名测试</text>
      <input class="form-input" 
             type="text" 
             placeholder="请输入姓名"
             value="{{testData.name}}"
             bindinput="onInputChange"
             data-field="name" />
      <text class="display-value">当前值: {{testData.name || '空'}}</text>
    </view>

    <view class="form-group">
      <text class="form-label">手机号测试</text>
      <input class="form-input" 
             type="number" 
             placeholder="请输入手机号"
             value="{{testData.phone}}"
             bindinput="onInputChange"
             data-field="phone" />
      <text class="display-value">当前值: {{testData.phone || '空'}}</text>
    </view>

    <view class="form-group">
      <text class="form-label">身份证测试</text>
      <input class="form-input" 
             type="idcard" 
             placeholder="请输入身份证号"
             value="{{testData.idCard}}"
             bindinput="onInputChange"
             data-field="idCard" />
      <text class="display-value">当前值: {{testData.idCard || '空'}}</text>
    </view>

    <button class="test-btn" bindtap="clearAll">清空所有</button>
    <button class="test-btn" bindtap="fillTest">填入测试数据</button>
  </view>
</view>
