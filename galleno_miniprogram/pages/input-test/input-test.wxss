/* input-test.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 32rpx;
}

.test-section {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 32rpx;
  text-align: center;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #ffffff;
  color: #1f2937;
  line-height: 1.5;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
}

.display-value {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-top: 8rpx;
  padding: 8rpx 12rpx;
  background: #f9fafb;
  border-radius: 8rpx;
}

.test-btn {
  width: 100%;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.test-btn::after {
  border: none;
}
